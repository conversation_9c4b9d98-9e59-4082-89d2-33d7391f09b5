<template>
  <a-drawer v-model:open="openDrawer" placement="right" :width="500" :maskClosable="false" title="编辑用户" @after-open-change="afterOpenChange" @close="closeDrawer">
    <a-form ref="formRef" :model="formState" :rules="rules" :label-col="{ style: { width: '100px' } }" :wrapper-col="{ span: 17 }">
      <!-- 基本信息 -->
      <a-form-item label="姓名" name="real_name">
        <a-input placeholder="请输入姓名" v-model:value="formState.real_name" :maxlength="30" @input="handleInputChange" :disabled="isInternalCompany" />
      </a-form-item>

      <a-form-item label="注册方式" name="type">
        <a-radio-group v-model:value="formState.type" @change="handleRegistrationTypeChange" :disabled="isInternalCompany">
          <a-radio :value="1">手机号</a-radio>
          <a-radio :value="2">邮箱</a-radio>
        </a-radio-group>
      </a-form-item>

      <!-- 手机号注册 -->
      <template v-if="formState.type === 1">
        <a-form-item label="手机号码" name="account">
          <a-input-group compact>
            <a-select
              v-model:value="formState.area_code"
              :options="countryCodeOptions"
              showSearch
              :filter-option="filterCountryOption"
              @change="handleCountryCodeChange"
              style="width: 120px"
              placeholder="+86"
              disabled
            />
            <a-input v-model:value="formState.account" placeholder="请输入手机号码" :maxlength="30" @input="handlePhoneInput" style="width: calc(100% - 120px)" :disabled="isInternalCompany" />
          </a-input-group>
        </a-form-item>
      </template>

      <!-- 邮箱注册 -->
      <template v-if="formState.type === 2">
        <a-form-item label="邮箱" name="account">
          <a-input placeholder="请输入邮箱" v-model:value="formState.account" :maxlength="100" @input="handleEmailInput" :disabled="isInternalCompany" />
        </a-form-item>
      </template>
      <a-form-item label="所属部门" name="department_ids">
        <a-select
          showArrow
          mode="multiple"
          placeholder="请选择所属部门"
          v-model:value="formState.department_ids"
          :options="departmentOption"
          @click.stop="isInternalCompany ? null : openDepartmentSelect()"
          @deselect="isInternalCompany ? null : handleDepartmentDeselect"
          :open="false"
          :disabled="isInternalCompany"
        >
          <template #tagRender="{ label, closable, onClose, value }">
            <a-tag
              class="dragTag"
              :closable="closable && !isInternalCompany"
              style="display: flex; align-items: center; margin: 2px; font-size: 12px"
              :style="{ cursor: isInternalCompany ? 'default' : 'pointer' }"
              :key="value"
              @close="isInternalCompany ? null : onClose"
              @click.stop
            >
              <span>{{ label }}</span>
            </a-tag>
          </template>
        </a-select>
      </a-form-item>

      <a-form-item label="直属上级" name="leader_id">
        <a-select
          showArrow
          allowClear
          showSearch
          optionFilterProp="account_name"
          placeholder="请输入姓名模糊搜索"
          v-model:value="formState.leader_id"
          :options="leaderOption"
          :field-names="{ label: 'account_name', value: 'id' }"
          @search="handleLeaderSearch"
          :filter-option="false"
          :disabled="isInternalCompany"
        />
      </a-form-item>
      <a-form-item label="系统角色" name="role_ids">
        <a-select showArrow showSearch optionFilterProp="label" :placeholder="roleDisplayPlaceholder" :value="displayRoleValue" :options="roleOptionsForEdit" @change="handleRoleChange" allowClear>
          <!-- 自定义显示选中的值 -->
          <template #suffixIcon v-if="isSuperAdmin">
            <span></span>
          </template>
          <template #empty>
            <div class="text-gray-500">暂无角色选项</div>
          </template>
        </a-select>
      </a-form-item>
    </a-form>

    <!-- 底部按钮 -->
    <template #footer>
      <div class="gap-12px flex">
        <a-button :loading="submitLoading" type="primary" @click="handleSave">保存</a-button>
        <a-button :loading="submitLoading" @click="closeDrawer">取消</a-button>
      </div>
    </template>
  </a-drawer>
  <select-depart v-if="showSelectDepart" ref="selectDepartRef" @change="handleDepartmentChange" :tree-data="departmentTreeData" @close="showSelectDepart = false" />
</template>

<script setup lang="ts">
import { ref, computed, nextTick, onMounted } from 'vue'
import type { FormInstance } from 'ant-design-vue'
import { message } from 'ant-design-vue'
import { UpdateOuterAccount, GetUserInfo, GetAccountSelectOption } from '@/servers/UserManager'
import { GetList as GetRoleList } from '@/servers/RoleNew'
import { GetCompanyTree } from '@/servers/CompanyArchitecture'
// import { GetList as GetCountryList } from '@/servers/CountryRegion'

import Sortable from 'sortablejs'
import { cloneDeep, createLoadingDebounce } from '@/utils'
import SelectDepart from './SelectDepart.vue'

const emit = defineEmits(['query', 'close'])
const openDrawer = ref(false)
const selectDepartRef = ref()
// const dropFlag = ref(false)
const submitLoading = ref(false)
const formRef = ref<FormInstance>()
// 表单数据
const formState = ref<any>({
  department: [],
})

// 保存用户输入的手机号和邮箱值
const userInputValues = ref({
  phone: '', // 用户输入的手机号
  email: '', // 用户输入的邮箱
})

// 保存从接口获取的原始数据
const originalData = ref({
  phone: '', // 接口返回的手机号
  email: '', // 接口返回的邮箱,
  role_ids: '', // 接口返回的角色ID
})
// 部门树数据
const departmentTreeData = ref<any[]>([])
// 企业下拉框
const enterpriseOption = ref<any[]>([])
// 部门下拉框
const departmentOption = ref<any[]>([])
// 直接上级下拉框
const leaderOption = ref<any[]>([])
// 系统角色下拉框
// const roleOption = ref<any[]>([])
// 显示模式的角色选项（包含超级管理员）
// const roleOptionsForDisplay = ref<any[]>([])
// 编辑模式的角色选项（排除超级管理员）
const roleOptionsForEdit = ref<any[]>([])
// 当前是否处于编辑模式
const isRoleEditMode = ref(false)
// 国家码下拉框
const countryCodeOptions = ref<any[]>([])
// 当前企业名称（用于显示）
const currentCompanyName = ref('')

// 防抖定时器
let searchTimeout: any = null

const showSelectDepart = ref(false)

// 判断是否为超级管理员角色
// const isSuperAdminRole = (roleId: string) => {
//   return ['1', '2', '1,2'].includes(roleId)
// }

// 根据当前模式返回对应的角色选项
// const currentRoleOptions = computed(() => {
//   return isRoleEditMode.value ? roleOptionsForEdit.value : roleOptionsForDisplay.value
// })

// 计算属性：角色显示的占位符文本
const roleDisplayPlaceholder = computed(() => {
  // 检查原始用户数据中是否是超级管理员
  if (originalData.value.role_ids) {
    const isSuperAdmin = originalData.value.role_ids === '1' || originalData.value.role_ids === '2'

    if (isSuperAdmin) {
      return '超级管理员'
    }
  }

  return '请选择系统角色'
})

// 计算属性：显示的角色值
const displayRoleValue = computed(() => {
  // 如果是超级管理员或空字符串，返回空值
  if (isSuperAdmin.value || formState.value.role_ids === '') {
    return undefined
  }

  return formState.value.role_ids
})

// 计算属性：是否为超级管理员
const isSuperAdmin = computed(() => {
  const roleIds = originalData.value.role_ids ?? ''
  return roleIds === '1' || roleIds === '2'
})

// 是否为内部企业
const isInternalCompany = ref(false)
// const isInternalCompany = computed(() => {
//   // 根据用户所属企业的company_category_type判断
//   // 假设内部企业的company_category_type为1，外部企业为其他值
//   // return currentCompanyInfo.value.company_category_type === 1
//   const userDataStr = localStorage.getItem('userData')
//   if (!userDataStr) return false
//   try {
//     const userData = JSON.parse(userDataStr)
//     console.log('userData', userData)
//     return userData.scope === 1
//   } catch (e) {
//     console.error('解析用户数据失败:', e)
//     return false
//   }
// })

// 表单验证规则（响应式）
const rules = computed(() => {
  return {
    real_name: [
      { required: !isInternalCompany.value, message: '请输入姓名', trigger: 'blur' },
      { max: 30, message: '姓名不能超过30个字符', trigger: 'blur' },
      {
        validator: (_, value) => {
          if (!value) return Promise.resolve()
          // 去除空格后检查
          const trimmedValue = value.trim()
          if (trimmedValue !== value) {
            return Promise.reject('姓名不能包含空格')
          }
          return Promise.resolve()
        },
        trigger: 'blur',
      },
    ],
    type: [{ required: !isInternalCompany.value, message: '请选择注册方式', trigger: 'change' }],
    account: [
      { required: !isInternalCompany.value, message: formState.value.type === 1 ? '请输入手机号码' : '请输入邮箱', trigger: 'blur' },
      {
        validator: (_, value) => {
          if (!value) return Promise.resolve()

          // 去除空格
          const trimmedValue = value.trim()
          if (trimmedValue !== value) {
            return Promise.reject('不能包含空格')
          }

          if (formState.value.type === 1) {
            // 手机号验证
            if (!/^\d+$/.test(value)) {
              return Promise.reject('手机号只能包含数字')
            }
            if (value.length > 30) {
              return Promise.reject('手机号不能超过30个字符')
            }
            // 检查是否选择了国家码
            if (!formState.value.area_code) {
              return Promise.reject('请选择手机国家码')
            }
            // 中国手机号前缀验证
            if (formState.value.area_code === '+86') {
              if (!/^(13|14|15|16|17|18|19)/.test(value)) {
                return Promise.reject('中国手机号前2位需为13、14、15、16、17、18、19')
              }
            }
          } else {
            // 邮箱验证
            if (value.length > 100) {
              return Promise.reject('邮箱不能超过100个字符')
            }
            if (/[\u4e00-\u9fa5]/.test(value)) {
              return Promise.reject('邮箱不能包含中文字符')
            }
            // 基本邮箱格式验证
            if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
              return Promise.reject('请输入正确的邮箱格式')
            }
          }
          return Promise.resolve()
        },
        trigger: 'blur',
      },
    ],
    company_id: [{ required: true, message: '请选择所属企业', trigger: 'change' }],
    department_ids: [{ required: true, message: '请选择所属部门', trigger: 'change' }],
    role_ids: [{ required: true, message: '请选择系统角色', trigger: 'change' }],
  }
})
// 获取国家码列表
const GetCountryCodeList = async () => {
  try {
    // const res = await GetCountryList({ page: 1, pageSize: 500, status: 1 })
    // if (res.success) {
    //   countryCodeOptions.value = res.data.list.map((item: any) => ({
    //     label: `${item.country_region_code} ${item.country_region_name}`,
    //     value: item.country_region_code,
    //   }))
    //   // 确保+86在列表中
    //   if (!countryCodeOptions.value.find((item) => item.value === '+86')) {
    //     countryCodeOptions.value.unshift({ label: '+86 中国', value: '+86' })
    //   }
    // }
  } catch (error) {
    console.error('获取国家码列表失败:', error)
    // 提供默认的国家码选项
    countryCodeOptions.value = [{ label: '+86 ', value: '+86' }]
  }
}

// 获取直接上级下拉框
const GetLeaderList = (id: any, searchText = '') => {
  return GetAccountSelectOption({
    company_id: id,
    // scope: '内部联系人',
    status: ['启用'],
    search_text: searchText,
  }).then((res) => {
    leaderOption.value = res.data.map((item: any) => ({
      ...item,
      account_name: `${item.real_name}（${item.account_id}）`,
    }))
  })
}

// 输入处理函数 - 去除空格
const handleInputChange = (e: any) => {
  const value = e.target.value
  formState.value.real_name = value.replace(/\s/g, '')
}

// 注册方式变化处理
const handleRegistrationTypeChange = () => {
  if (formState.value.type === 1) {
    // 切换到手机号
    formState.value.area_code = formState.value.area_code || '+86'

    // 如果用户有输入过手机号，就显示用户输入的值
    if (userInputValues.value.phone) {
      formState.value.account = userInputValues.value.phone
    } else {
      // 如果用户没有输入过，就显示接口返回的手机号
      formState.value.account = originalData.value.phone || ''
    }
  } else {
    // 切换到邮箱
    formState.value.area_code = ''

    // 如果用户有输入过邮箱，就显示用户输入的值
    if (userInputValues.value.email) {
      formState.value.account = userInputValues.value.email
    } else {
      // 如果用户没有输入过，就显示接口返回的邮箱
      formState.value.account = originalData.value.email || ''
    }
  }
}

// 国家码选择过滤
const filterCountryOption = (input: string, option: any) => {
  return option.label.toLowerCase().includes(input.toLowerCase())
}

// 国家码变化处理
const handleCountryCodeChange = () => {
  // 不清空手机号，保留用户输入的内容
  // if (formState.value.account) {
  //   formState.value.account = ''
  // }
}

// 手机号输入处理
const handlePhoneInput = (e: any) => {
  let value = e.target.value
  // 只允许数字
  value = value.replace(/\D/g, '')
  // 去除空格
  value = value.replace(/\s/g, '')
  formState.value.account = value
  // 保存用户输入的手机号
  userInputValues.value.phone = value
}

// 邮箱输入处理
const handleEmailInput = (e: any) => {
  let value = e.target.value
  // 去除空格和中文
  value = value.replace(/\s/g, '').replace(/[\u4e00-\u9fa5]/g, '')
  formState.value.account = value
  // 保存用户输入的邮箱
  userInputValues.value.email = value
}

// 上级搜索处理（防抖）
const handleLeaderSearch = (value: string) => {
  if (searchTimeout) {
    clearTimeout(searchTimeout)
  }
  searchTimeout = setTimeout(() => {
    if (formState.value.company_id) {
      GetLeaderList(formState.value.company_id, value)
    }
  }, 300)
}

// 获取成员详情
// 获取成员详情（修复后）
const getMemberInfo = (id: string) => {
  return GetUserInfo({
    umcAccountId: id,
  }).then((res) => {
    const userData = res.data || {}
    console.log('EditMembers - 用户数据:', userData)
    isInternalCompany.value = userData.scope === 1
    log
    // 处理角色信息 - 如果是超级管理员，设置为空值以便使用 placeholder 显示
    let currentRoleIds = ''
    if (userData.role_ids) {
      const isSuperAdmin = userData.role_ids === '1' || userData.role_ids === '2'

      // 只有非超级管理员角色才设置到表单中，超级管理员通过 placeholder 显示
      if (!isSuperAdmin) {
        currentRoleIds = userData.role_ids
      }
      originalData.value.role_ids = userData.role_ids
    }

    // 保存原始数据
    originalData.value.phone = ''
    originalData.value.email = ''

    // 从接口数据中提取手机号和邮箱
    if (userData.phones && userData.phones.length > 0) {
      originalData.value.phone = userData.phones[0]
    }
    if (userData.emails && userData.emails.length > 0) {
      originalData.value.email = userData.emails[0]
    }

    // 如果没有phones和emails，从account_name判断
    if (!originalData.value.phone && !originalData.value.email && userData.account_name) {
      if (userData.account_name.includes('@')) {
        originalData.value.email = userData.account_name
      } else {
        originalData.value.phone = userData.account_name
      }
    }

    // 判断注册类型：优先手机号
    let registrationType = 1 // 默认手机号
    let accountValue = ''
    let areaCode = '+86' // 默认为中国区号

    if (originalData.value.phone) {
      registrationType = 1
      accountValue = originalData.value.phone
      areaCode = '+86'
    } else if (originalData.value.email) {
      registrationType = 2
      accountValue = originalData.value.email
      areaCode = ''
    }

    // 初始化用户输入值为空（表示用户还没有输入）
    userInputValues.value.phone = ''
    userInputValues.value.email = ''

    formState.value = {
      ...userData,
      real_name: userData.real_name || '',
      type: registrationType, // 1=手机号, 2=邮箱
      account: accountValue,
      area_code: areaCode,
      leader_id: userData.leader_id || null,
      company_id: userData.company_id || '',
      role_ids: currentRoleIds,
      department: userData.department || [],
      department_ids: userData.department ? userData.department.map((item: any) => item.id) : [],
      scope: 1, // 内部联系人
    }

    console.log('EditMembers - 解析后的表单数据:', {
      type: formState.value.type,
      account: formState.value.account,
      area_code: formState.value.area_code,
    })

    // 设置企业名称显示
    if (userData.company_name) {
      currentCompanyName.value = userData.company_name
    }

    departmentOption.value = userData.department
      ? userData.department.map((item: any, index: any) => ({
          label: item.name || item.department_name,
          value: item.id,
          index,
        }))
      : []
  })
}

// 获取角色数据并填充两个数组
const GetRoleListData = (searchText = '') => {
  return GetRoleList({ page: 1, pageSize: 500, search_text: searchText }).then((res) => {
    const activeRoles = res.data.list.filter((item: any) => item.status === 1)
    const baseRoles = activeRoles.map((item: any) => ({
      label: item.role_name,
      value: `${item.id}`,
    }))

    // 编辑模式的角色选项（排除超级管理员）
    roleOptionsForEdit.value = baseRoles.filter((item: any) => !['1', '2'].includes(item.value))

    // // 显示模式的角色选项（包含超级管理员）
    // const displayRoles = [...baseRoles]
    //
    // // 添加超级管理员选项（仅用于显示当前角色）
    // const hasSuperAdmin1 = displayRoles.some((item) => item.value === '1')
    // const hasSuperAdmin2 = displayRoles.some((item) => item.value === '2')
    //
    // if (!hasSuperAdmin1) {
    //   displayRoles.unshift({ label: '超级管理员', value: '1' })
    // }
    // if (!hasSuperAdmin2) {
    //   displayRoles.unshift({ label: '超级管理员', value: '2' })
    // }
    //
    // // 添加组合超级管理员选项（用于显示多角色情况）
    // displayRoles.unshift({ label: '超级管理员', value: '1,2' })
    //
    // roleOptionsForDisplay.value = displayRoles

    // 初始化时使用显示模式
    isRoleEditMode.value = false
  })
}

// // 处理角色选择框点击事件
// const handleRoleSelectClick = () => {
//   console.log('用户点击角色下拉框，切换到编辑模式')
//
//   // 检查当前用户是否是超级管理员
//   const currentRoleIds = formState.value.role_ids
//     ? formState.value.role_ids
//         .toString()
//         .split(',')
//         .map((id: string) => id.trim())
//     : []
//   const isSuperAdmin = currentRoleIds.some((id: string) => ['1', '2'].includes(id))
//
//   if (isSuperAdmin) {
//     // 如果是超级管理员，清空当前角色选择
//     formState.value.role_ids = ''
//   }
//
//   // 切换到编辑模式（不会重新请求数据，避免闪烁）
//   isRoleEditMode.value = true
// }

// 处理角色选择变化
const handleRoleChange = (val: any) => {
  formState.value.role_ids = val
  console.log('角色选择已更新:', val)
}

const setPathName = (data: any, treeData: any) => {
  data.forEach((item: any) => {
    treeData.forEach((item1: any) => {
      if (item.id == item1.id) {
        item.company_name = item1.company_name
      }
      if (item1.childs && item1.childs.length > 0) {
        setPathName(data, item1.childs)
      }
    })
  })
}

// 获取部门下拉框
const GetDepartmentList = (companyId: any) => {
  return GetCompanyTree({}).then((res) => {
    // 查找指定企业下的所有部门数据
    const findCompanyTree = (nodes: any[], targetId: string): any[] => {
      for (const node of nodes) {
        if (node.id === targetId && node.type === 1) {
          // 找到目标企业，返回包含企业本身的完整树结构
          return [node]
        }
        if (node.childs && node.childs.length > 0) {
          const result = findCompanyTree(node.childs, targetId)
          if (result.length > 0) {
            return result
          }
        }
      }
      return []
    }

    const companyTree = findCompanyTree(res.data || [], companyId)
    setPathName(formState.value.department, companyTree)

    // 标准化数据结构，确保 SelectDepart 组件能正确显示
    const normalizeTreeData = (nodes: any[]): any[] => {
      return nodes.map((node) => ({
        ...node,
        department_name: node.name || node.department_name || '', // 确保有 department_name 字段
        company_name: node.full_name || node.company_name || '',
        childs: node.childs && node.childs.length > 0 ? normalizeTreeData(node.childs) : [],
      }))
    }

    departmentTreeData.value = normalizeTreeData(companyTree) // 保存标准化后的树形数据
  })
}

// 获取企业下拉框
const GetEnterpriseList = () => {
  return GetCompanyTree({}).then((res) => {
    // 递归查找所有企业节点（type=1）
    const filterCompanies = (nodes: any[]): any[] => {
      const companies: any[] = []

      const traverse = (nodeList: any[]) => {
        nodeList.forEach((node) => {
          if (node.type === 1) {
            companies.push({
              label: node.department_name || node.company_name,
              value: node.id,
              company_category_type: node.company_category_type || 0,
            })
          }
          if (node.childs && node.childs.length > 0) {
            traverse(node.childs)
          }
        })
      }

      traverse(nodes)
      return companies
    }

    enterpriseOption.value = filterCompanies(res.data || [])
  })
}
// 打开抽屉
const showDrawer = async (row: any) => {
  openDrawer.value = true
  await getMemberInfo(row.id)
  // 在获取用户信息后重新获取角色列表，确保超级管理员角色能正确回显
  await GetRoleListData()
  await GetEnterpriseList()
  await GetCountryCodeList()
  await GetLeaderList(formState.value.company_id)
  await GetDepartmentList(formState.value.company_id)

  // 如果当前用户有leader_id，但在leaderOption中找不到，则单独添加该leader信息
  if (formState.value.leader_id && leaderOption.value.length > 0) {
    const hasLeader = leaderOption.value.some((item: any) => String(item.id) === String(formState.value.leader_id))
    if (!hasLeader) {
      // 添加当前leader到选项中
      if (formState.value.leader_name) {
        ;(leaderOption.value as any[]).unshift({
          id: String(formState.value.leader_id),
          account_name: formState.value.leader_name,
          account_name_lebel: `${formState.value.leader_name}( ${formState.value.leader_id} )`,
          company_name: '其他企业',
        })
      }
    }
  }

  nextTick(() => {
    onDrop()
  })
}

// const limitEnglishAndSpecialChars = () => {
//   // 正则只能输入英文字符、数字、特殊字符
//   const regex = /[\u4e00-\u9fa5]/

//   if (regex.test(formState.value.job_id)) {
//     formState.value.job_id = formState.value.job_id.replace(/[\u4E00-\u9FA5]/g, '')
//   }
// }
// 打开部门选择对话框
const openDepartmentSelect = async () => {
  // 确保有部门数据，如果没有则先获取
  if (!departmentTreeData.value || departmentTreeData.value.length === 0) {
    if (formState.value.company_id) {
      await GetDepartmentList(formState.value.company_id)
    }
  }

  showSelectDepart.value = true
  await nextTick()

  // 从部门树数据中查找完整的部门信息，补充缺失的 company_name
  const findDeptInTree = (deptId: string, treeData: any[]): any => {
    for (const node of treeData) {
      if (String(node.id) === String(deptId)) {
        return node
      }
      if (node.childs && node.childs.length > 0) {
        const found = findDeptInTree(deptId, node.childs)
        if (found) return found
      }
    }
    return null
  }

  const arr = formState.value.department.map((item: any) => {
    // 从树数据中查找完整信息
    const fullDeptInfo = findDeptInTree(item.id, departmentTreeData.value)

    return {
      id: item.id,
      department_name: item.name || item.department_name,
      type: item.type,
      // 优先使用树数据中的完整路径，如果没有则使用原有数据
      company_name: fullDeptInfo?.company_name || fullDeptInfo?.full_name || item.company_name || item.full_name || '',
    }
  })

  console.log('=== 编辑用户部门选择调试 ===')
  console.log('原始部门数据:', formState.value.department)
  console.log('处理后的部门数组:', arr)
  console.log('部门树数据:', departmentTreeData.value)

  selectDepartRef.value?.showModal(arr, departmentTreeData.value)
}
// 处理部门选择变化
const handleDepartmentChange = async (departments: any[]) => {
  // const flag = formState.value.department_ids.length == 0 || !formState.value.department_ids
  showSelectDepart.value = false
  formState.value.department = departments
  formState.value.department_ids = formState.value.department.map((item: any) => item.id)
  // 更新选项显示
  departmentOption.value = departments.map((item, index: any) => ({
    label: item.department_name,
    value: item.id,
    key: item.id,
    index,
  }))

  // nextTick(() => {
  //   console.log('变化')
  //   if (flag && !dropFlag.value) {
  //     onDrop() // 重新初始化拖拽功能
  //     dropFlag.value = true
  //   }
  // })
}
// 处理部门标签删除
const handleDepartmentDeselect = (value: string) => {
  // 更新 department 数组
  formState.value.department = formState.value.department.filter((item: any) => item.id !== value)
  // 更新选项显示
  departmentOption.value = formState.value.department.map((item: any, index: any) => ({
    label: item.department_name,
    value: item.id,
    index,
    key: item.id,
  }))
}
// 关闭抽屉
const closeDrawer = () => {
  openDrawer.value = false
  emit('close')
}

// 编辑用户的核心逻辑
const editUserCore = async () => {
  try {
    await formRef.value?.validate()

    // 处理角色ID：如果是超级管理员且当前角色为空，则使用原始角色ID
    let roleIdsToSave = formState.value.role_ids || ''
    if (isSuperAdmin.value && !roleIdsToSave) {
      roleIdsToSave = originalData.value.role_ids || ''
    }

    // 构造保存的参数
    const companyBinds = formState.value.department_ids.map((item: any, index: any) => {
      return {
        company_id: String(item),
        is_main: index === 0 ? 1 : 0, // 第一个为主部门
      }
    })

    const params = {
      id: formState.value.id,
      real_name: formState.value.real_name,
      type: formState.value.type === 1 ? '1' : '2',
      account: formState.value.account,
      area_code: formState.value.type === 1 ? formState.value.area_code : '',
      leader_id: formState.value.leader_id || 0,
      company_id: formState.value.company_id,
      role_ids: roleIdsToSave,
      companyBinds,
      scope: '1', // 内部联系人=1
    }

    await UpdateOuterAccount(params)
    message.success('保存成功')
    emit('query')
    closeDrawer()
  } catch (error: any) {
    // 获取第一个错误信息
    if (error.errorFields) {
      const firstError = error.errorFields[0]
      switch (firstError.name[0]) {
        case 'real_name':
          message.error('请输入姓名')
          break
        case 'type':
          message.error('请选择注册方式')
          break
        case 'account':
          message.error(formState.value.type === 1 ? '请输入手机号码' : '请输入邮箱')
          break
        case 'company_id':
          message.error('请选择所属企业')
          break
        case 'department_ids':
          message.error('请选择所属部门')
          break
        case 'role_ids':
          message.error('请选择系统角色')
          break
        default:
          message.error(firstError.errors[0])
      }
    } else {
      message.error(error.message || '保存失败')
    }
    throw error
  }
}

// 带防抖和loading的保存函数
const handleSave = createLoadingDebounce(editUserCore, submitLoading, 1000)

// 抽屉状态改变后的回调
const afterOpenChange = (status: boolean) => {
  if (!status) {
    formRef.value?.resetFields()
    // 清空数据
    Object.assign(formState.value, {
      user_name: '',
      account_id: '',
      account_name: '',
      real_name: '',
      job_id: '',
      company: '',
      job_name: '',
      leader_id: null,
      role_names: '',
      department: [],
    })

    // 清空用户输入缓存和原始数据
    userInputValues.value.phone = ''
    userInputValues.value.email = ''
    originalData.value.phone = ''
    originalData.value.email = ''
  }
}

const onDrop = () => {
  nextTick(() => {
    // 确保 DOM 已渲染
    const el: HTMLElement | null = document.querySelector('.ant-select-selection-overflow')
    console.log(el)

    if (!el) return
    new Sortable(el, {
      animation: 300,
      handle: '.dragTag', // 拖拽手柄
      delay: 10, // 拖拽延迟
      forceFallback: true, // 强制使用原生拖拽
      onEnd: (item) => {
        const { oldIndex, newIndex } = item
        console.log('Drag Result:', oldIndex, newIndex)

        const currRow = formState.value.department_ids.splice(oldIndex, 1)[0]
        const arr = cloneDeep(formState.value.department_ids)
        arr.splice(newIndex, 0, currRow)

        const currRow1 = formState.value.department.splice(oldIndex, 1)[0]
        const arr1 = cloneDeep(formState.value.department)
        arr1.splice(newIndex, 0, currRow1)

        nextTick(() => {
          // 确保 DOM 同步更新
          formState.value.department_ids = arr
          formState.value.department = arr1
          departmentOption.value = formState.value.department.map((item: any, index: any) => ({
            label: item.name || item.department_name,
            value: item.id,
            index,
          }))
        })
      },
    })
  })
}

onMounted(() => {
  // 预加载角色数据
  GetRoleListData().catch((error) => {
    console.error('预加载角色数据失败:', error)
  })
})

defineExpose({
  showDrawer,
})
</script>

<style lang="scss" scoped>
:deep(.ant-form) {
  .ant-form-item {
    margin-bottom: 16px;
  }
}
</style>
